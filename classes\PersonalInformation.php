<?php
/**
 * Personal Information Class for PDS System
 */

class PersonalInformation {
    private $conn;
    private $table_name = "personal_information";

    public $id;
    public $user_id;
    public $surname;
    public $first_name;
    public $middle_name;
    public $name_extension;
    public $date_of_birth;
    public $place_of_birth;
    public $sex;
    public $civil_status;
    public $height;
    public $weight;
    public $blood_type;
    public $gsis_id;
    public $pag_ibig_id;
    public $philhealth_no;
    public $sss_no;
    public $tin_no;
    public $agency_employee_no;
    public $citizenship;
    public $dual_citizenship_country;
    public $residential_address;
    public $residential_zipcode;
    public $permanent_address;
    public $permanent_zipcode;
    public $telephone_no;
    public $mobile_no;
    public $email_address;

    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Create personal information record
     */
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET user_id=:user_id, surname=:surname, first_name=:first_name, 
                      middle_name=:middle_name, name_extension=:name_extension,
                      date_of_birth=:date_of_birth, place_of_birth=:place_of_birth,
                      sex=:sex, civil_status=:civil_status, height=:height, weight=:weight,
                      blood_type=:blood_type, gsis_id=:gsis_id, pag_ibig_id=:pag_ibig_id,
                      philhealth_no=:philhealth_no, sss_no=:sss_no, tin_no=:tin_no,
                      agency_employee_no=:agency_employee_no, citizenship=:citizenship,
                      dual_citizenship_country=:dual_citizenship_country,
                      residential_address=:residential_address, residential_zipcode=:residential_zipcode,
                      permanent_address=:permanent_address, permanent_zipcode=:permanent_zipcode,
                      telephone_no=:telephone_no, mobile_no=:mobile_no, email_address=:email_address";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->sanitizeInputs();

        // Bind values
        $stmt->bindParam(":user_id", $this->user_id);
        $stmt->bindParam(":surname", $this->surname);
        $stmt->bindParam(":first_name", $this->first_name);
        $stmt->bindParam(":middle_name", $this->middle_name);
        $stmt->bindParam(":name_extension", $this->name_extension);
        $stmt->bindParam(":date_of_birth", $this->date_of_birth);
        $stmt->bindParam(":place_of_birth", $this->place_of_birth);
        $stmt->bindParam(":sex", $this->sex);
        $stmt->bindParam(":civil_status", $this->civil_status);
        $stmt->bindParam(":height", $this->height);
        $stmt->bindParam(":weight", $this->weight);
        $stmt->bindParam(":blood_type", $this->blood_type);
        $stmt->bindParam(":gsis_id", $this->gsis_id);
        $stmt->bindParam(":pag_ibig_id", $this->pag_ibig_id);
        $stmt->bindParam(":philhealth_no", $this->philhealth_no);
        $stmt->bindParam(":sss_no", $this->sss_no);
        $stmt->bindParam(":tin_no", $this->tin_no);
        $stmt->bindParam(":agency_employee_no", $this->agency_employee_no);
        $stmt->bindParam(":citizenship", $this->citizenship);
        $stmt->bindParam(":dual_citizenship_country", $this->dual_citizenship_country);
        $stmt->bindParam(":residential_address", $this->residential_address);
        $stmt->bindParam(":residential_zipcode", $this->residential_zipcode);
        $stmt->bindParam(":permanent_address", $this->permanent_address);
        $stmt->bindParam(":permanent_zipcode", $this->permanent_zipcode);
        $stmt->bindParam(":telephone_no", $this->telephone_no);
        $stmt->bindParam(":mobile_no", $this->mobile_no);
        $stmt->bindParam(":email_address", $this->email_address);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    /**
     * Update personal information record
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . " 
                  SET surname=:surname, first_name=:first_name, middle_name=:middle_name,
                      name_extension=:name_extension, date_of_birth=:date_of_birth,
                      place_of_birth=:place_of_birth, sex=:sex, civil_status=:civil_status,
                      height=:height, weight=:weight, blood_type=:blood_type,
                      gsis_id=:gsis_id, pag_ibig_id=:pag_ibig_id, philhealth_no=:philhealth_no,
                      sss_no=:sss_no, tin_no=:tin_no, agency_employee_no=:agency_employee_no,
                      citizenship=:citizenship, dual_citizenship_country=:dual_citizenship_country,
                      residential_address=:residential_address, residential_zipcode=:residential_zipcode,
                      permanent_address=:permanent_address, permanent_zipcode=:permanent_zipcode,
                      telephone_no=:telephone_no, mobile_no=:mobile_no, email_address=:email_address,
                      updated_at=NOW()
                  WHERE id=:id AND user_id=:user_id";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->sanitizeInputs();

        // Bind values
        $stmt->bindParam(":surname", $this->surname);
        $stmt->bindParam(":first_name", $this->first_name);
        $stmt->bindParam(":middle_name", $this->middle_name);
        $stmt->bindParam(":name_extension", $this->name_extension);
        $stmt->bindParam(":date_of_birth", $this->date_of_birth);
        $stmt->bindParam(":place_of_birth", $this->place_of_birth);
        $stmt->bindParam(":sex", $this->sex);
        $stmt->bindParam(":civil_status", $this->civil_status);
        $stmt->bindParam(":height", $this->height);
        $stmt->bindParam(":weight", $this->weight);
        $stmt->bindParam(":blood_type", $this->blood_type);
        $stmt->bindParam(":gsis_id", $this->gsis_id);
        $stmt->bindParam(":pag_ibig_id", $this->pag_ibig_id);
        $stmt->bindParam(":philhealth_no", $this->philhealth_no);
        $stmt->bindParam(":sss_no", $this->sss_no);
        $stmt->bindParam(":tin_no", $this->tin_no);
        $stmt->bindParam(":agency_employee_no", $this->agency_employee_no);
        $stmt->bindParam(":citizenship", $this->citizenship);
        $stmt->bindParam(":dual_citizenship_country", $this->dual_citizenship_country);
        $stmt->bindParam(":residential_address", $this->residential_address);
        $stmt->bindParam(":residential_zipcode", $this->residential_zipcode);
        $stmt->bindParam(":permanent_address", $this->permanent_address);
        $stmt->bindParam(":permanent_zipcode", $this->permanent_zipcode);
        $stmt->bindParam(":telephone_no", $this->telephone_no);
        $stmt->bindParam(":mobile_no", $this->mobile_no);
        $stmt->bindParam(":email_address", $this->email_address);
        $stmt->bindParam(":id", $this->id);
        $stmt->bindParam(":user_id", $this->user_id);

        return $stmt->execute();
    }

    /**
     * Get personal information by user ID
     */
    public function getByUserId($user_id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE user_id = :user_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $this->populateFromArray($row);
            return true;
        }

        return false;
    }

    /**
     * Get personal information by ID
     */
    public function getById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $this->populateFromArray($row);
            return true;
        }

        return false;
    }

    /**
     * Check if user has personal information
     */
    public function userHasPersonalInfo($user_id) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE user_id = :user_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();

        return $stmt->rowCount() > 0;
    }

    /**
     * Validate personal information data
     */
    public function validate() {
        $errors = [];

        // Required fields validation
        if (empty($this->surname)) {
            $errors[] = "Surname is required.";
        }

        if (empty($this->first_name)) {
            $errors[] = "First name is required.";
        }

        if (empty($this->date_of_birth)) {
            $errors[] = "Date of birth is required.";
        } elseif (!$this->isValidDate($this->date_of_birth)) {
            $errors[] = "Invalid date of birth format.";
        }

        if (empty($this->place_of_birth)) {
            $errors[] = "Place of birth is required.";
        }

        if (empty($this->sex) || !in_array($this->sex, ['Male', 'Female'])) {
            $errors[] = "Valid sex selection is required.";
        }

        if (empty($this->civil_status) || !in_array($this->civil_status, ['Single', 'Married', 'Widowed', 'Separated', 'Others'])) {
            $errors[] = "Valid civil status selection is required.";
        }

        if (empty($this->citizenship)) {
            $errors[] = "Citizenship is required.";
        }

        if (empty($this->residential_address)) {
            $errors[] = "Residential address is required.";
        }

        if (empty($this->permanent_address)) {
            $errors[] = "Permanent address is required.";
        }

        // Optional field validations
        if (!empty($this->height) && ($this->height < 0 || $this->height > 3)) {
            $errors[] = "Height must be between 0 and 3 meters.";
        }

        if (!empty($this->weight) && ($this->weight < 0 || $this->weight > 300)) {
            $errors[] = "Weight must be between 0 and 300 kg.";
        }

        if (!empty($this->email_address) && !filter_var($this->email_address, FILTER_VALIDATE_EMAIL)) {
            $errors[] = "Invalid email address format.";
        }

        return $errors;
    }

    /**
     * Sanitize all input properties
     */
    private function sanitizeInputs() {
        $this->surname = htmlspecialchars(strip_tags($this->surname));
        $this->first_name = htmlspecialchars(strip_tags($this->first_name));
        $this->middle_name = htmlspecialchars(strip_tags($this->middle_name));
        $this->name_extension = htmlspecialchars(strip_tags($this->name_extension));
        $this->place_of_birth = htmlspecialchars(strip_tags($this->place_of_birth));
        $this->sex = htmlspecialchars(strip_tags($this->sex));
        $this->civil_status = htmlspecialchars(strip_tags($this->civil_status));
        $this->blood_type = htmlspecialchars(strip_tags($this->blood_type));
        $this->gsis_id = htmlspecialchars(strip_tags($this->gsis_id));
        $this->pag_ibig_id = htmlspecialchars(strip_tags($this->pag_ibig_id));
        $this->philhealth_no = htmlspecialchars(strip_tags($this->philhealth_no));
        $this->sss_no = htmlspecialchars(strip_tags($this->sss_no));
        $this->tin_no = htmlspecialchars(strip_tags($this->tin_no));
        $this->agency_employee_no = htmlspecialchars(strip_tags($this->agency_employee_no));
        $this->citizenship = htmlspecialchars(strip_tags($this->citizenship));
        $this->dual_citizenship_country = htmlspecialchars(strip_tags($this->dual_citizenship_country));
        $this->residential_address = htmlspecialchars(strip_tags($this->residential_address));
        $this->residential_zipcode = htmlspecialchars(strip_tags($this->residential_zipcode));
        $this->permanent_address = htmlspecialchars(strip_tags($this->permanent_address));
        $this->permanent_zipcode = htmlspecialchars(strip_tags($this->permanent_zipcode));
        $this->telephone_no = htmlspecialchars(strip_tags($this->telephone_no));
        $this->mobile_no = htmlspecialchars(strip_tags($this->mobile_no));
        $this->email_address = htmlspecialchars(strip_tags($this->email_address));
    }

    /**
     * Populate object properties from array
     */
    private function populateFromArray($data) {
        $this->id = $data['id'];
        $this->user_id = $data['user_id'];
        $this->surname = $data['surname'];
        $this->first_name = $data['first_name'];
        $this->middle_name = $data['middle_name'];
        $this->name_extension = $data['name_extension'];
        $this->date_of_birth = $data['date_of_birth'];
        $this->place_of_birth = $data['place_of_birth'];
        $this->sex = $data['sex'];
        $this->civil_status = $data['civil_status'];
        $this->height = $data['height'];
        $this->weight = $data['weight'];
        $this->blood_type = $data['blood_type'];
        $this->gsis_id = $data['gsis_id'];
        $this->pag_ibig_id = $data['pag_ibig_id'];
        $this->philhealth_no = $data['philhealth_no'];
        $this->sss_no = $data['sss_no'];
        $this->tin_no = $data['tin_no'];
        $this->agency_employee_no = $data['agency_employee_no'];
        $this->citizenship = $data['citizenship'];
        $this->dual_citizenship_country = $data['dual_citizenship_country'];
        $this->residential_address = $data['residential_address'];
        $this->residential_zipcode = $data['residential_zipcode'];
        $this->permanent_address = $data['permanent_address'];
        $this->permanent_zipcode = $data['permanent_zipcode'];
        $this->telephone_no = $data['telephone_no'];
        $this->mobile_no = $data['mobile_no'];
        $this->email_address = $data['email_address'];
    }

    /**
     * Validate date format
     */
    private function isValidDate($date) {
        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }

    /**
     * Get full name
     */
    public function getFullName() {
        $name_parts = array_filter([
            $this->first_name,
            $this->middle_name,
            $this->surname,
            $this->name_extension
        ]);
        
        return implode(' ', $name_parts);
    }

    /**
     * Get age from date of birth
     */
    public function getAge() {
        if (empty($this->date_of_birth)) {
            return null;
        }

        $birth_date = new DateTime($this->date_of_birth);
        $today = new DateTime();
        $age = $today->diff($birth_date);

        return $age->y;
    }
}
?>
