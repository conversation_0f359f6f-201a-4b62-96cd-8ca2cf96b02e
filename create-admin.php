<?php
/**
 * Create Admin User Script
 */

echo "<h1>Create Admin User</h1>";

try {
    $pdo = new PDO("mysql:host=localhost;dbname=sunegros_pds", 'root', '', array(
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ));
    
    // Check if admin user already exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = 'admin'");
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: orange;'>Admin user already exists!</p>";
    } else {
        // Create admin user
        $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("INSERT INTO users 
            (employee_id, username, email, password_hash, role, first_name, last_name, status) 
            VALUES ('ADMIN001', 'admin', '<EMAIL>', ?, 'admin', 'System', 'Administrator', 'active')");
        
        if ($stmt->execute([$password_hash])) {
            echo "<p style='color: green;'>✓ Admin user created successfully!</p>";
            echo "<p><strong>Username:</strong> admin</p>";
            echo "<p><strong>Password:</strong> admin123</p>";
            echo "<p style='color: red;'><strong>Important:</strong> Please change the password after first login!</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to create admin user</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>Go to Main Page</a></p>";
echo "<p><a href='auth/login.php'>Login Page</a></p>";
?>
