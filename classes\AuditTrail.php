<?php
/**
 * Audit Trail Class for logging user activities
 */

class AuditTrail {
    private $conn;
    private $table_name = "audit_trail";

    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Log an activity
     */
    public function log($user_id, $action, $table_name = null, $record_id = null, $old_values = null, $new_values = null, $ip_address = null, $user_agent = null) {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET user_id=:user_id, action=:action, table_name=:table_name, 
                      record_id=:record_id, old_values=:old_values, new_values=:new_values,
                      ip_address=:ip_address, user_agent=:user_agent";

        $stmt = $this->conn->prepare($query);

        // Convert arrays to JSON
        $old_values_json = $old_values ? json_encode($old_values) : null;
        $new_values_json = $new_values ? json_encode($new_values) : null;

        $stmt->bindParam(":user_id", $user_id);
        $stmt->bindParam(":action", $action);
        $stmt->bindParam(":table_name", $table_name);
        $stmt->bindParam(":record_id", $record_id);
        $stmt->bindParam(":old_values", $old_values_json);
        $stmt->bindParam(":new_values", $new_values_json);
        $stmt->bindParam(":ip_address", $ip_address);
        $stmt->bindParam(":user_agent", $user_agent);

        return $stmt->execute();
    }

    /**
     * Get audit logs for a specific user
     */
    public function getUserLogs($user_id, $limit = 50, $offset = 0) {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE user_id = :user_id 
                  ORDER BY created_at DESC 
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->bindParam(":offset", $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get all audit logs (for admin)
     */
    public function getAllLogs($limit = 100, $offset = 0) {
        $query = "SELECT at.*, u.username, u.first_name, u.last_name 
                  FROM " . $this->table_name . " at
                  LEFT JOIN users u ON at.user_id = u.id
                  ORDER BY at.created_at DESC 
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->bindParam(":offset", $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get logs by date range
     */
    public function getLogsByDateRange($start_date, $end_date, $limit = 100, $offset = 0) {
        $query = "SELECT at.*, u.username, u.first_name, u.last_name 
                  FROM " . $this->table_name . " at
                  LEFT JOIN users u ON at.user_id = u.id
                  WHERE DATE(at.created_at) BETWEEN :start_date AND :end_date
                  ORDER BY at.created_at DESC 
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":start_date", $start_date);
        $stmt->bindParam(":end_date", $end_date);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->bindParam(":offset", $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get total count of logs
     */
    public function getTotalCount() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $row['total'];
    }

    /**
     * Clean old logs (older than specified days)
     */
    public function cleanOldLogs($days = 365) {
        $query = "DELETE FROM " . $this->table_name . " 
                  WHERE created_at < DATE_SUB(NOW(), INTERVAL :days DAY)";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":days", $days, PDO::PARAM_INT);
        
        return $stmt->execute();
    }
}
?>
