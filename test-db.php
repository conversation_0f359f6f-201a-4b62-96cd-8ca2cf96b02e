<?php
/**
 * Simple Database Connection Test
 */

echo "<h1>Database Connection Test</h1>";

try {
    // Test connection without database
    $pdo = new PDO("mysql:host=localhost", 'root', '', array(
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ));
    echo "<p style='color: green;'>✓ MySQL connection successful</p>";
    
    // Check if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE 'sunegros_pds'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Database 'sunegros_pds' exists</p>";
        
        // Test connection with database
        $pdo_db = new PDO("mysql:host=localhost;dbname=sunegros_pds", 'root', '', array(
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ));
        echo "<p style='color: green;'>✓ Database connection successful</p>";
        
        // Check tables
        $stmt = $pdo_db->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (count($tables) > 0) {
            echo "<p style='color: green;'>✓ Found " . count($tables) . " tables:</p>";
            echo "<ul>";
            foreach ($tables as $table) {
                echo "<li>$table</li>";
            }
            echo "</ul>";
            
            // Check admin user
            $stmt = $pdo_db->query("SELECT * FROM users WHERE username = 'admin'");
            if ($stmt->rowCount() > 0) {
                echo "<p style='color: green;'>✓ Admin user exists</p>";
            } else {
                echo "<p style='color: orange;'>⚠ Admin user not found</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ No tables found</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Database 'sunegros_pds' does not exist</p>";
        echo "<p><a href='database/install.php'>Install Database</a></p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>Go to Main Page</a></p>";
echo "<p><a href='database/install.php'>Database Installation</a></p>";
?>
