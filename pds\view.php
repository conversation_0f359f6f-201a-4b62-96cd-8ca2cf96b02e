<?php
/**
 * View Personal Data Sheet - State University of Northern Negros PDS System
 */

require_once '../config/config.php';
require_once '../classes/User.php';
require_once '../classes/PersonalInformation.php';

// Require login
require_login();

$page_title = 'View Personal Data Sheet';
$additional_css = ['assets/css/pds-form.css'];

// Get user's PDS data
$personal_info = null;
$has_pds = false;

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $personal_info = new PersonalInformation($db);
    $has_pds = $personal_info->getByUserId($_SESSION['user_id']);
    
} catch (Exception $e) {
    error_log("PDS View Error: " . $e->getMessage());
    $error_message = "Unable to load your Personal Data Sheet.";
}

include '../includes/header.php';
?>

<?php if (isset($_SESSION['success_message'])): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($_SESSION['success_message']); ?>
    </div>
    <?php unset($_SESSION['success_message']); ?>
<?php endif; ?>

<?php if (isset($_SESSION['error_message'])): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($_SESSION['error_message']); ?>
    </div>
    <?php unset($_SESSION['error_message']); ?>
<?php endif; ?>

<?php if (!$has_pds): ?>
    <div class="pds-container">
        <div class="text-center" style="padding: 4rem 2rem;">
            <i class="fas fa-file-alt" style="font-size: 4rem; color: var(--medium-gray); margin-bottom: 2rem;"></i>
            <h2>No Personal Data Sheet Found</h2>
            <p>You haven't created your Personal Data Sheet yet.</p>
            <a href="create.php" class="btn btn-primary">
                <i class="fas fa-plus"></i> Create PDS
            </a>
        </div>
    </div>
<?php else: ?>

<div class="pds-container">
    <div class="pds-header">
        <div class="header-logo">
            <img src="../assets/images/sunegros-logo.png" alt="State University of Northern Negros" class="university-logo" onerror="this.style.display='none'">
        </div>
        <div class="header-content">
            <h1>State University of Northern Negros</h1>
            <h2>PERSONAL DATA SHEET</h2>
            <div class="pds-info">
                <p><strong>Employee ID:</strong> <?php echo htmlspecialchars($_SESSION['employee_id']); ?></p>
                <p><strong>Generated:</strong> <?php echo date('F j, Y g:i A'); ?></p>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="pds-actions">
        <a href="edit.php" class="btn btn-primary">
            <i class="fas fa-edit"></i> Edit PDS
        </a>
        <a href="export.php" class="btn btn-secondary" target="_blank">
            <i class="fas fa-download"></i> Export PDF
        </a>
        <button onclick="window.print()" class="btn btn-secondary">
            <i class="fas fa-print"></i> Print
        </button>
        <a href="../dashboard/" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>

    <!-- I. PERSONAL INFORMATION -->
    <div class="pds-section">
        <div class="section-header">
            <h3>I. PERSONAL INFORMATION</h3>
        </div>
        
        <div class="info-grid">
            <div class="info-row">
                <div class="info-group">
                    <label>1. SURNAME</label>
                    <div class="info-value"><?php echo htmlspecialchars($personal_info->surname); ?></div>
                </div>
            </div>
            
            <div class="info-row">
                <div class="info-group">
                    <label>FIRST NAME</label>
                    <div class="info-value"><?php echo htmlspecialchars($personal_info->first_name); ?></div>
                </div>
                <div class="info-group">
                    <label>MIDDLE NAME</label>
                    <div class="info-value"><?php echo htmlspecialchars($personal_info->middle_name ?: 'N/A'); ?></div>
                </div>
            </div>
            
            <div class="info-row">
                <div class="info-group">
                    <label>NAME EXTENSION</label>
                    <div class="info-value"><?php echo htmlspecialchars($personal_info->name_extension ?: 'N/A'); ?></div>
                </div>
            </div>

            <div class="info-row">
                <div class="info-group">
                    <label>2. DATE OF BIRTH</label>
                    <div class="info-value">
                        <?php 
                        if ($personal_info->date_of_birth) {
                            echo date('F j, Y', strtotime($personal_info->date_of_birth));
                            echo ' (Age: ' . $personal_info->getAge() . ')';
                        } else {
                            echo 'N/A';
                        }
                        ?>
                    </div>
                </div>
                <div class="info-group">
                    <label>3. PLACE OF BIRTH</label>
                    <div class="info-value"><?php echo htmlspecialchars($personal_info->place_of_birth); ?></div>
                </div>
            </div>

            <div class="info-row">
                <div class="info-group">
                    <label>4. SEX</label>
                    <div class="info-value"><?php echo htmlspecialchars($personal_info->sex); ?></div>
                </div>
                <div class="info-group">
                    <label>5. CIVIL STATUS</label>
                    <div class="info-value"><?php echo htmlspecialchars($personal_info->civil_status); ?></div>
                </div>
            </div>

            <div class="info-row">
                <div class="info-group">
                    <label>6. HEIGHT (m)</label>
                    <div class="info-value"><?php echo $personal_info->height ? number_format($personal_info->height, 2) : 'N/A'; ?></div>
                </div>
                <div class="info-group">
                    <label>7. WEIGHT (kg)</label>
                    <div class="info-value"><?php echo $personal_info->weight ? number_format($personal_info->weight, 1) : 'N/A'; ?></div>
                </div>
                <div class="info-group">
                    <label>8. BLOOD TYPE</label>
                    <div class="info-value"><?php echo htmlspecialchars($personal_info->blood_type ?: 'N/A'); ?></div>
                </div>
            </div>

            <div class="info-section">
                <h4>Government IDs</h4>
                <div class="info-row">
                    <div class="info-group">
                        <label>9. GSIS ID NO.</label>
                        <div class="info-value"><?php echo htmlspecialchars($personal_info->gsis_id ?: 'N/A'); ?></div>
                    </div>
                    <div class="info-group">
                        <label>10. PAG-IBIG ID NO.</label>
                        <div class="info-value"><?php echo htmlspecialchars($personal_info->pag_ibig_id ?: 'N/A'); ?></div>
                    </div>
                </div>
                
                <div class="info-row">
                    <div class="info-group">
                        <label>11. PHILHEALTH NO.</label>
                        <div class="info-value"><?php echo htmlspecialchars($personal_info->philhealth_no ?: 'N/A'); ?></div>
                    </div>
                    <div class="info-group">
                        <label>12. SSS NO.</label>
                        <div class="info-value"><?php echo htmlspecialchars($personal_info->sss_no ?: 'N/A'); ?></div>
                    </div>
                </div>
                
                <div class="info-row">
                    <div class="info-group">
                        <label>13. TIN NO.</label>
                        <div class="info-value"><?php echo htmlspecialchars($personal_info->tin_no ?: 'N/A'); ?></div>
                    </div>
                    <div class="info-group">
                        <label>14. AGENCY EMPLOYEE NO.</label>
                        <div class="info-value"><?php echo htmlspecialchars($personal_info->agency_employee_no ?: 'N/A'); ?></div>
                    </div>
                </div>
            </div>

            <div class="info-section">
                <h4>Citizenship</h4>
                <div class="info-row">
                    <div class="info-group">
                        <label>15. CITIZENSHIP</label>
                        <div class="info-value">
                            <?php echo htmlspecialchars($personal_info->citizenship); ?>
                            <?php if ($personal_info->dual_citizenship_country): ?>
                                <br><small>Dual Citizenship: <?php echo htmlspecialchars($personal_info->dual_citizenship_country); ?></small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="info-section">
                <h4>Address Information</h4>
                <div class="info-row">
                    <div class="info-group full-width">
                        <label>16. RESIDENTIAL ADDRESS</label>
                        <div class="info-value">
                            <?php echo nl2br(htmlspecialchars($personal_info->residential_address)); ?>
                            <?php if ($personal_info->residential_zipcode): ?>
                                <br><strong>ZIP CODE:</strong> <?php echo htmlspecialchars($personal_info->residential_zipcode); ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="info-row">
                    <div class="info-group full-width">
                        <label>17. PERMANENT ADDRESS</label>
                        <div class="info-value">
                            <?php echo nl2br(htmlspecialchars($personal_info->permanent_address)); ?>
                            <?php if ($personal_info->permanent_zipcode): ?>
                                <br><strong>ZIP CODE:</strong> <?php echo htmlspecialchars($personal_info->permanent_zipcode); ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="info-section">
                <h4>Contact Information</h4>
                <div class="info-row">
                    <div class="info-group">
                        <label>18. TELEPHONE NO.</label>
                        <div class="info-value"><?php echo htmlspecialchars($personal_info->telephone_no ?: 'N/A'); ?></div>
                    </div>
                    <div class="info-group">
                        <label>19. MOBILE NO.</label>
                        <div class="info-value"><?php echo htmlspecialchars($personal_info->mobile_no ?: 'N/A'); ?></div>
                    </div>
                </div>
                
                <div class="info-row">
                    <div class="info-group">
                        <label>20. E-MAIL ADDRESS</label>
                        <div class="info-value"><?php echo htmlspecialchars($personal_info->email_address ?: 'N/A'); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional sections will be added here -->
    <div class="pds-section">
        <div class="section-header">
            <h3>II. FAMILY BACKGROUND</h3>
        </div>
        <div class="info-message">
            <i class="fas fa-info-circle"></i>
            Family background information will be displayed here once completed.
        </div>
    </div>

    <div class="pds-section">
        <div class="section-header">
            <h3>III. EDUCATIONAL BACKGROUND</h3>
        </div>
        <div class="info-message">
            <i class="fas fa-info-circle"></i>
            Educational background information will be displayed here once completed.
        </div>
    </div>
</div>

<style>
.pds-actions {
    padding: 1rem 2rem;
    background: var(--light-gray);
    border-bottom: 1px solid var(--medium-gray);
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.pds-info {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--light-gray);
    border-radius: 8px;
}

.pds-info p {
    margin: 0.25rem 0;
    font-size: 0.9rem;
}

.info-grid {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.info-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.info-group {
    display: flex;
    flex-direction: column;
}

.info-group.full-width {
    grid-column: 1 / -1;
}

.info-group label {
    font-weight: 600;
    color: var(--dark-gray);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    text-transform: uppercase;
}

.info-value {
    padding: 0.75rem;
    background: white;
    border: 2px solid var(--medium-gray);
    border-radius: 4px;
    min-height: 2.5rem;
    display: flex;
    align-items: center;
}

.info-section {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--medium-gray);
}

.info-section h4 {
    margin: 0 0 1rem 0;
    color: var(--primary-color);
    font-size: 1rem;
    text-transform: uppercase;
    font-weight: bold;
}

.info-message {
    text-align: center;
    padding: 2rem;
    color: var(--dark-gray);
    font-style: italic;
}

.info-message i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

@media (max-width: 768px) {
    .pds-actions {
        flex-direction: column;
    }
    
    .info-row {
        grid-template-columns: 1fr;
    }
}

@media print {
    .pds-actions {
        display: none !important;
    }
}
</style>

<?php endif; ?>

<?php include '../includes/footer.php'; ?>
