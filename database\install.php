<?php
/**
 * Database Installation Script for State University of Northern Negros PDS System
 */

require_once '../config/config.php';

// Check if database is already installed
function isDatabaseInstalled() {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $stmt = $conn->query("SHOW TABLES LIKE 'users'");
        return $stmt->rowCount() > 0;
    } catch (Exception $e) {
        return false;
    }
}

// Install database schema
function installDatabase() {
    try {
        // Read SQL schema file
        $sql = file_get_contents('schema.sql');
        
        if (!$sql) {
            throw new Exception("Could not read schema.sql file");
        }
        
        // Split SQL into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        // Connect to MySQL server (without database)
        $pdo = new PDO("mysql:host=localhost", 'root', '', array(
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ));
        
        // Execute each statement
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }

        // Create default admin user
        createDefaultAdmin($pdo);

        return true;
    } catch (Exception $e) {
        throw new Exception("Database installation failed: " . $e->getMessage());
    }
}

// Create default admin user
function createDefaultAdmin($pdo) {
    try {
        // Check if admin user already exists
        $stmt = $pdo->prepare("SELECT id FROM sunegros_pds.users WHERE username = 'admin'");
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            // Create admin user with hashed password
            $password_hash = password_hash('admin123', PASSWORD_DEFAULT);

            $stmt = $pdo->prepare("INSERT INTO sunegros_pds.users
                (employee_id, username, email, password_hash, role, first_name, last_name, status)
                VALUES ('ADMIN001', 'admin', '<EMAIL>', ?, 'admin', 'System', 'Administrator', 'active')");

            $stmt->execute([$password_hash]);
        }
    } catch (Exception $e) {
        error_log("Error creating admin user: " . $e->getMessage());
    }
}

// Handle installation request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['install'])) {
    try {
        if (isDatabaseInstalled()) {
            $message = "Database is already installed!";
            $messageType = "warning";
        } else {
            installDatabase();
            $message = "Database installed successfully! You can now use the system.";
            $messageType = "success";
        }
    } catch (Exception $e) {
        $message = "Installation failed: " . $e->getMessage();
        $messageType = "danger";
    }
}

$page_title = "Database Installation";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <div class="container">
        <div class="form-container" style="max-width: 600px; margin: 50px auto;">
            <h1 class="text-center">Database Installation</h1>
            <p class="text-center">State University of Northern Negros Personal Data Sheet System</p>
            
            <?php if (isset($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <?php if (!isDatabaseInstalled()): ?>
                <div class="card">
                    <div class="card-body">
                        <h3>Installation Requirements</h3>
                        <ul>
                            <li>MySQL/MariaDB server running</li>
                            <li>PHP with PDO MySQL extension</li>
                            <li>Database user with CREATE privileges</li>
                        </ul>
                        
                        <h3>What will be installed:</h3>
                        <ul>
                            <li>Database: <strong>sunegros_pds</strong></li>
                            <li>All required tables and relationships</li>
                            <li>Default admin user (username: admin, password: admin123)</li>
                            <li>Indexes for optimal performance</li>
                        </ul>
                        
                        <form method="POST" class="mt-3">
                            <button type="submit" name="install" class="btn btn-primary" style="width: 100%;">
                                Install Database
                            </button>
                        </form>
                    </div>
                </div>
            <?php else: ?>
                <div class="alert alert-success">
                    <strong>Database is already installed!</strong><br>
                    You can proceed to use the system.
                </div>
                <div class="text-center">
                    <a href="../index.php" class="btn btn-primary">Go to System</a>
                </div>
            <?php endif; ?>
            
            <div class="text-center mt-3">
                <small>Make sure to change the default admin password after installation.</small>
            </div>
        </div>
    </div>
</body>
</html>
