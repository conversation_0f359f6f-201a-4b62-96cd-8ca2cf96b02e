<?php
/**
 * User Class for Authentication and User Management
 */

class User {
    private $conn;
    private $table_name = "users";

    public $id;
    public $employee_id;
    public $username;
    public $email;
    public $password_hash;
    public $role;
    public $first_name;
    public $last_name;
    public $status;
    public $created_at;
    public $updated_at;
    public $last_login;

    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Create a new user
     */
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET employee_id=:employee_id, username=:username, email=:email, 
                      password_hash=:password_hash, role=:role, first_name=:first_name, 
                      last_name=:last_name, status=:status";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->employee_id = htmlspecialchars(strip_tags($this->employee_id));
        $this->username = htmlspecialchars(strip_tags($this->username));
        $this->email = htmlspecialchars(strip_tags($this->email));
        $this->first_name = htmlspecialchars(strip_tags($this->first_name));
        $this->last_name = htmlspecialchars(strip_tags($this->last_name));
        $this->role = htmlspecialchars(strip_tags($this->role));
        $this->status = htmlspecialchars(strip_tags($this->status));

        // Hash password
        $this->password_hash = password_hash($this->password_hash, PASSWORD_DEFAULT);

        // Bind values
        $stmt->bindParam(":employee_id", $this->employee_id);
        $stmt->bindParam(":username", $this->username);
        $stmt->bindParam(":email", $this->email);
        $stmt->bindParam(":password_hash", $this->password_hash);
        $stmt->bindParam(":role", $this->role);
        $stmt->bindParam(":first_name", $this->first_name);
        $stmt->bindParam(":last_name", $this->last_name);
        $stmt->bindParam(":status", $this->status);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    /**
     * Authenticate user login
     */
    public function login($username, $password) {
        $query = "SELECT id, employee_id, username, email, password_hash, role, 
                         first_name, last_name, status 
                  FROM " . $this->table_name . " 
                  WHERE (username = :username OR email = :username) AND status = 'active'";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":username", $username);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (password_verify($password, $row['password_hash'])) {
                $this->id = $row['id'];
                $this->employee_id = $row['employee_id'];
                $this->username = $row['username'];
                $this->email = $row['email'];
                $this->role = $row['role'];
                $this->first_name = $row['first_name'];
                $this->last_name = $row['last_name'];
                $this->status = $row['status'];

                // Update last login
                $this->updateLastLogin();
                
                return true;
            }
        }

        return false;
    }

    /**
     * Update last login timestamp
     */
    private function updateLastLogin() {
        $query = "UPDATE " . $this->table_name . " SET last_login = NOW() WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();
    }

    /**
     * Check if username exists
     */
    public function usernameExists($username) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE username = :username";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":username", $username);
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    }

    /**
     * Check if email exists
     */
    public function emailExists($email) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE email = :email";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":email", $email);
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    }

    /**
     * Check if employee ID exists
     */
    public function employeeIdExists($employee_id) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE employee_id = :employee_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":employee_id", $employee_id);
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    }

    /**
     * Get user by ID
     */
    public function getById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $this->id = $row['id'];
            $this->employee_id = $row['employee_id'];
            $this->username = $row['username'];
            $this->email = $row['email'];
            $this->role = $row['role'];
            $this->first_name = $row['first_name'];
            $this->last_name = $row['last_name'];
            $this->status = $row['status'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            $this->last_login = $row['last_login'];
            
            return true;
        }

        return false;
    }

    /**
     * Update user information
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . " 
                  SET employee_id=:employee_id, username=:username, email=:email, 
                      role=:role, first_name=:first_name, last_name=:last_name, 
                      status=:status, updated_at=NOW() 
                  WHERE id=:id";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->employee_id = htmlspecialchars(strip_tags($this->employee_id));
        $this->username = htmlspecialchars(strip_tags($this->username));
        $this->email = htmlspecialchars(strip_tags($this->email));
        $this->first_name = htmlspecialchars(strip_tags($this->first_name));
        $this->last_name = htmlspecialchars(strip_tags($this->last_name));
        $this->role = htmlspecialchars(strip_tags($this->role));
        $this->status = htmlspecialchars(strip_tags($this->status));

        // Bind values
        $stmt->bindParam(":employee_id", $this->employee_id);
        $stmt->bindParam(":username", $this->username);
        $stmt->bindParam(":email", $this->email);
        $stmt->bindParam(":role", $this->role);
        $stmt->bindParam(":first_name", $this->first_name);
        $stmt->bindParam(":last_name", $this->last_name);
        $stmt->bindParam(":status", $this->status);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    /**
     * Change password
     */
    public function changePassword($new_password) {
        $query = "UPDATE " . $this->table_name . " 
                  SET password_hash=:password_hash, updated_at=NOW() 
                  WHERE id=:id";

        $stmt = $this->conn->prepare($query);
        
        $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
        
        $stmt->bindParam(":password_hash", $password_hash);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    /**
     * Generate password reset token
     */
    public function generatePasswordResetToken() {
        $token = bin2hex(random_bytes(32));
        $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
        
        $query = "UPDATE " . $this->table_name . " 
                  SET password_reset_token=:token, password_reset_expires=:expires 
                  WHERE id=:id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":token", $token);
        $stmt->bindParam(":expires", $expires);
        $stmt->bindParam(":id", $this->id);

        if ($stmt->execute()) {
            return $token;
        }

        return false;
    }

    /**
     * Verify password reset token
     */
    public function verifyPasswordResetToken($token) {
        $query = "SELECT id FROM " . $this->table_name . " 
                  WHERE password_reset_token=:token 
                  AND password_reset_expires > NOW()";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":token", $token);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            return $this->getById($row['id']);
        }

        return false;
    }

    /**
     * Clear password reset token
     */
    public function clearPasswordResetToken() {
        $query = "UPDATE " . $this->table_name . " 
                  SET password_reset_token=NULL, password_reset_expires=NULL 
                  WHERE id=:id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        
        return $stmt->execute();
    }

    /**
     * Get all users (for admin)
     */
    public function getAll($limit = 50, $offset = 0) {
        $query = "SELECT id, employee_id, username, email, role, first_name, 
                         last_name, status, created_at, last_login 
                  FROM " . $this->table_name . " 
                  ORDER BY created_at DESC 
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->bindParam(":offset", $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get total user count
     */
    public function getTotalCount() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $row['total'];
    }
}
?>
