<?php
/**
 * Common Header Template for State University of Northern Negros PDS System
 */

if (!isset($_SESSION)) {
    session_start();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?><?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css): ?>
            <link rel="stylesheet" href="<?php echo BASE_URL . $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-university"></i>
                    <h1><?php echo APP_NAME; ?></h1>
                </div>
                
                <?php if (is_logged_in()): ?>
                <div class="user-menu">
                    <div class="user-info">
                        <i class="fas fa-user"></i>
                        <span>Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                        <span class="badge"><?php echo ucfirst($_SESSION['user_role']); ?></span>
                    </div>
                    <a href="<?php echo BASE_URL; ?>auth/logout.php" class="btn btn-danger">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </header>

    <?php if (is_logged_in()): ?>
    <nav class="nav">
        <div class="container">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="<?php echo BASE_URL; ?>dashboard/" class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'index.php' && strpos($_SERVER['REQUEST_URI'], 'dashboard') !== false) ? 'active' : ''; ?>">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo BASE_URL; ?>pds/view.php" class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], 'pds') !== false) ? 'active' : ''; ?>">
                        <i class="fas fa-file-alt"></i> My PDS
                    </a>
                </li>
                <?php if ($_SESSION['user_role'] == 'admin'): ?>
                <li class="nav-item">
                    <a href="<?php echo BASE_URL; ?>admin/employees.php" class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], 'admin') !== false) ? 'active' : ''; ?>">
                        <i class="fas fa-users"></i> Manage Employees
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo BASE_URL; ?>admin/reports.php" class="nav-link">
                        <i class="fas fa-chart-bar"></i> Reports
                    </a>
                </li>
                <?php endif; ?>
                <li class="nav-item">
                    <a href="<?php echo BASE_URL; ?>profile/settings.php" class="nav-link">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                </li>
            </ul>
        </div>
    </nav>
    <?php endif; ?>

    <main class="main-content">
        <div class="container">
