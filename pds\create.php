<?php
/**
 * Create Personal Data Sheet - State University of Northern Negros PDS System
 */

require_once '../config/config.php';
require_once '../classes/User.php';

// Require login
require_login();

// Check if user already has PDS
try {
    $database = new Database();
    $db = $database->getConnection();
    
    $check_query = "SELECT id FROM personal_information WHERE user_id = :user_id";
    $stmt = $db->prepare($check_query);
    $stmt->bindParam(':user_id', $_SESSION['user_id']);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        header('Location: view.php');
        exit();
    }
} catch (Exception $e) {
    error_log("PDS check error: " . $e->getMessage());
}

$page_title = 'Create Personal Data Sheet';
$additional_css = ['assets/css/pds-form.css'];

include '../includes/header.php';
?>

<div class="pds-container">
    <div class="pds-header">
        <div class="header-logo">
            <img src="../assets/images/sunegros-logo.png" alt="State University of Northern Negros" class="university-logo" onerror="this.style.display='none'">
        </div>
        <div class="header-content">
            <h1>State University of Northern Negros</h1>
            <h2>PERSONAL DATA SHEET</h2>
            <p class="warning-text">
                <strong>WARNING:</strong> Any misrepresentation made in the Personal Data Sheet and the Work Experience Sheet 
                shall cause the filing of administrative/criminal case/s against the person concerned.
            </p>
            <p class="instruction-text">
                <strong>READ THE ATTACHED GUIDE TO FILLING OUT THE PERSONAL DATA SHEET (PDS) BEFORE ACCOMPLISHING THE PDS FORM.</strong>
            </p>
        </div>
    </div>

    <form id="pds-form" method="POST" action="process.php" data-validate="true" enctype="multipart/form-data">
        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
        <input type="hidden" name="action" value="create">

        <!-- I. PERSONAL INFORMATION -->
        <div class="pds-section">
            <div class="section-header">
                <h3>I. PERSONAL INFORMATION</h3>
            </div>
            
            <div class="form-grid">
                <!-- Name Fields -->
                <div class="form-row full-width">
                    <div class="form-group">
                        <label for="surname">1. SURNAME</label>
                        <input type="text" id="surname" name="surname" class="form-control" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="first_name">FIRST NAME</label>
                        <input type="text" id="first_name" name="first_name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="middle_name">MIDDLE NAME</label>
                        <input type="text" id="middle_name" name="middle_name" class="form-control">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="name_extension">NAME EXTENSION (JR., SR)</label>
                        <input type="text" id="name_extension" name="name_extension" class="form-control" placeholder="e.g., Jr., Sr., III">
                    </div>
                </div>

                <!-- Birth Information -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="date_of_birth">2. DATE OF BIRTH (mm/dd/yyyy)</label>
                        <input type="date" id="date_of_birth" name="date_of_birth" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="place_of_birth">3. PLACE OF BIRTH</label>
                        <input type="text" id="place_of_birth" name="place_of_birth" class="form-control" required>
                    </div>
                </div>

                <!-- Sex and Civil Status -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="sex">4. SEX</label>
                        <select id="sex" name="sex" class="form-control" required>
                            <option value="">Select Sex</option>
                            <option value="Male">Male</option>
                            <option value="Female">Female</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="civil_status">5. CIVIL STATUS</label>
                        <select id="civil_status" name="civil_status" class="form-control" required>
                            <option value="">Select Civil Status</option>
                            <option value="Single">Single</option>
                            <option value="Married">Married</option>
                            <option value="Widowed">Widowed</option>
                            <option value="Separated">Separated</option>
                            <option value="Others">Others</option>
                        </select>
                    </div>
                </div>

                <!-- Physical Attributes -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="height">6. HEIGHT (m)</label>
                        <input type="number" id="height" name="height" class="form-control" step="0.01" min="0" max="3" placeholder="e.g., 1.75">
                    </div>
                    <div class="form-group">
                        <label for="weight">7. WEIGHT (kg)</label>
                        <input type="number" id="weight" name="weight" class="form-control" step="0.1" min="0" max="300" placeholder="e.g., 70.5">
                    </div>
                    <div class="form-group">
                        <label for="blood_type">8. BLOOD TYPE</label>
                        <select id="blood_type" name="blood_type" class="form-control">
                            <option value="">Select Blood Type</option>
                            <option value="A+">A+</option>
                            <option value="A-">A-</option>
                            <option value="B+">B+</option>
                            <option value="B-">B-</option>
                            <option value="AB+">AB+</option>
                            <option value="AB-">AB-</option>
                            <option value="O+">O+</option>
                            <option value="O-">O-</option>
                        </select>
                    </div>
                </div>

                <!-- Government IDs -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="gsis_id">9. GSIS ID NO.</label>
                        <input type="text" id="gsis_id" name="gsis_id" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="pag_ibig_id">10. PAG-IBIG ID NO.</label>
                        <input type="text" id="pag_ibig_id" name="pag_ibig_id" class="form-control">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="philhealth_no">11. PHILHEALTH NO.</label>
                        <input type="text" id="philhealth_no" name="philhealth_no" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="sss_no">12. SSS NO.</label>
                        <input type="text" id="sss_no" name="sss_no" class="form-control">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="tin_no">13. TIN NO.</label>
                        <input type="text" id="tin_no" name="tin_no" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="agency_employee_no">14. AGENCY EMPLOYEE NO.</label>
                        <input type="text" id="agency_employee_no" name="agency_employee_no" class="form-control">
                    </div>
                </div>

                <!-- Citizenship -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="citizenship">15. CITIZENSHIP</label>
                        <select id="citizenship" name="citizenship" class="form-control" required>
                            <option value="">Select Citizenship</option>
                            <option value="Filipino">Filipino</option>
                            <option value="Dual Citizenship">Dual Citizenship</option>
                            <option value="Naturalized Filipino">Naturalized Filipino</option>
                        </select>
                    </div>
                    <div class="form-group" id="dual_citizenship_group" style="display: none;">
                        <label for="dual_citizenship_country">If holder of dual citizenship, please indicate the details.</label>
                        <input type="text" id="dual_citizenship_country" name="dual_citizenship_country" class="form-control" placeholder="Country">
                    </div>
                </div>

                <!-- Address Information -->
                <div class="address-section">
                    <h4>16. RESIDENTIAL ADDRESS</h4>
                    <div class="form-row">
                        <div class="form-group full-width">
                            <label for="residential_address">House/Block/Lot No., Street, Subdivision/Village, Barangay, City/Municipality, Province</label>
                            <textarea id="residential_address" name="residential_address" class="form-control" rows="2" required></textarea>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="residential_zipcode">ZIP CODE</label>
                            <input type="text" id="residential_zipcode" name="residential_zipcode" class="form-control">
                        </div>
                    </div>
                </div>

                <div class="address-section">
                    <h4>17. PERMANENT ADDRESS</h4>
                    <div class="form-row">
                        <div class="form-group full-width">
                            <label for="permanent_address">House/Block/Lot No., Street, Subdivision/Village, Barangay, City/Municipality, Province</label>
                            <textarea id="permanent_address" name="permanent_address" class="form-control" rows="2" required></textarea>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="permanent_zipcode">ZIP CODE</label>
                            <input type="text" id="permanent_zipcode" name="permanent_zipcode" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="same_as_residential"> Same as Residential Address
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="telephone_no">18. TELEPHONE NO.</label>
                        <input type="text" id="telephone_no" name="telephone_no" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="mobile_no">19. MOBILE NO.</label>
                        <input type="text" id="mobile_no" name="mobile_no" class="form-control">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="email_address">20. E-MAIL ADDRESS (if any)</label>
                        <input type="email" id="email_address" name="email_address" class="form-control">
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Buttons -->
        <div class="form-navigation">
            <button type="button" class="btn btn-secondary" onclick="window.location.href='../dashboard/'">
                <i class="fas fa-arrow-left"></i> Cancel
            </button>
            <button type="button" class="btn btn-primary" onclick="nextSection()">
                Next: Family Background <i class="fas fa-arrow-right"></i>
            </button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle citizenship change
    document.getElementById('citizenship').addEventListener('change', function() {
        const dualGroup = document.getElementById('dual_citizenship_group');
        if (this.value === 'Dual Citizenship') {
            dualGroup.style.display = 'block';
            document.getElementById('dual_citizenship_country').required = true;
        } else {
            dualGroup.style.display = 'none';
            document.getElementById('dual_citizenship_country').required = false;
        }
    });

    // Handle same as residential address
    document.getElementById('same_as_residential').addEventListener('change', function() {
        if (this.checked) {
            document.getElementById('permanent_address').value = document.getElementById('residential_address').value;
            document.getElementById('permanent_zipcode').value = document.getElementById('residential_zipcode').value;
        }
    });

    // Auto-copy residential to permanent when residential changes
    document.getElementById('residential_address').addEventListener('input', function() {
        if (document.getElementById('same_as_residential').checked) {
            document.getElementById('permanent_address').value = this.value;
        }
    });

    document.getElementById('residential_zipcode').addEventListener('input', function() {
        if (document.getElementById('same_as_residential').checked) {
            document.getElementById('permanent_zipcode').value = this.value;
        }
    });
});

function nextSection() {
    // Validate current section before proceeding
    if (PDS.validateForm(document.getElementById('pds-form'))) {
        // Save current progress and move to next section
        window.location.href = 'family-background.php';
    }
}
</script>

<?php include '../includes/footer.php'; ?>
