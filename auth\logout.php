<?php
/**
 * Logout Script for State University of Northern Negros PDS System
 */

require_once '../config/config.php';

// Log the logout if user is logged in
if (is_logged_in()) {
    try {
        require_once '../classes/AuditTrail.php';
        $database = new Database();
        $db = $database->getConnection();
        $audit = new AuditTrail($db);
        $audit->log($_SESSION['user_id'], 'LOGOUT', null, null, null, null, $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT']);
    } catch (Exception $e) {
        // Log error but continue with logout
        error_log("Logout audit error: " . $e->getMessage());
    }
}

// Clear all session variables
$_SESSION = array();

// Delete the session cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Clear remember me cookie if it exists
if (isset($_COOKIE['remember_token'])) {
    setcookie('remember_token', '', time() - 3600, '/');
}

// Destroy the session
session_destroy();

// Redirect to login page with logout message
header('Location: ' . BASE_URL . 'auth/login.php?logout=1');
exit();
?>
