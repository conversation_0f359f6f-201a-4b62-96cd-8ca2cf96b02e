<?php
/**
 * Process PDS Form Data - State University of Northern Negros PDS System
 */

require_once '../config/config.php';
require_once '../classes/User.php';
require_once '../classes/PersonalInformation.php';
require_once '../classes/AuditTrail.php';

// Require login
require_login();

// Only process POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: ../dashboard/');
    exit();
}

// Verify CSRF token
if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
    $_SESSION['error_message'] = 'Invalid security token. Please try again.';
    header('Location: create.php');
    exit();
}

$action = $_POST['action'] ?? '';
$response = ['success' => false, 'message' => '', 'errors' => []];

try {
    $database = new Database();
    $db = $database->getConnection();
    $audit = new AuditTrail($db);

    switch ($action) {
        case 'create':
        case 'update':
            $result = processPersonalInformation($db, $audit, $action);
            break;
            
        case 'save_family':
            $result = processFamilyBackground($db, $audit);
            break;
            
        case 'save_education':
            $result = processEducationalBackground($db, $audit);
            break;
            
        default:
            throw new Exception('Invalid action specified.');
    }

    if ($result['success']) {
        $_SESSION['success_message'] = $result['message'];
        
        // Redirect based on action
        if (isset($_POST['save_and_continue'])) {
            header('Location: ' . $result['redirect']);
        } else {
            header('Location: view.php');
        }
    } else {
        $_SESSION['error_message'] = $result['message'];
        $_SESSION['form_errors'] = $result['errors'];
        $_SESSION['form_data'] = $_POST;
        header('Location: ' . ($_POST['return_url'] ?? 'create.php'));
    }

} catch (Exception $e) {
    error_log("PDS Process Error: " . $e->getMessage());
    $_SESSION['error_message'] = 'An error occurred while processing your request. Please try again.';
    header('Location: ' . ($_POST['return_url'] ?? 'create.php'));
}

exit();

/**
 * Process Personal Information Section
 */
function processPersonalInformation($db, $audit, $action) {
    $personal_info = new PersonalInformation($db);
    
    // Set user ID
    $personal_info->user_id = $_SESSION['user_id'];
    
    // Populate from POST data
    $personal_info->surname = $_POST['surname'] ?? '';
    $personal_info->first_name = $_POST['first_name'] ?? '';
    $personal_info->middle_name = $_POST['middle_name'] ?? '';
    $personal_info->name_extension = $_POST['name_extension'] ?? '';
    $personal_info->date_of_birth = $_POST['date_of_birth'] ?? '';
    $personal_info->place_of_birth = $_POST['place_of_birth'] ?? '';
    $personal_info->sex = $_POST['sex'] ?? '';
    $personal_info->civil_status = $_POST['civil_status'] ?? '';
    $personal_info->height = !empty($_POST['height']) ? (float)$_POST['height'] : null;
    $personal_info->weight = !empty($_POST['weight']) ? (float)$_POST['weight'] : null;
    $personal_info->blood_type = $_POST['blood_type'] ?? '';
    $personal_info->gsis_id = $_POST['gsis_id'] ?? '';
    $personal_info->pag_ibig_id = $_POST['pag_ibig_id'] ?? '';
    $personal_info->philhealth_no = $_POST['philhealth_no'] ?? '';
    $personal_info->sss_no = $_POST['sss_no'] ?? '';
    $personal_info->tin_no = $_POST['tin_no'] ?? '';
    $personal_info->agency_employee_no = $_POST['agency_employee_no'] ?? '';
    $personal_info->citizenship = $_POST['citizenship'] ?? '';
    $personal_info->dual_citizenship_country = $_POST['dual_citizenship_country'] ?? '';
    $personal_info->residential_address = $_POST['residential_address'] ?? '';
    $personal_info->residential_zipcode = $_POST['residential_zipcode'] ?? '';
    $personal_info->permanent_address = $_POST['permanent_address'] ?? '';
    $personal_info->permanent_zipcode = $_POST['permanent_zipcode'] ?? '';
    $personal_info->telephone_no = $_POST['telephone_no'] ?? '';
    $personal_info->mobile_no = $_POST['mobile_no'] ?? '';
    $personal_info->email_address = $_POST['email_address'] ?? '';

    // Validate data
    $validation_errors = $personal_info->validate();
    if (!empty($validation_errors)) {
        return [
            'success' => false,
            'message' => 'Please correct the following errors:',
            'errors' => $validation_errors
        ];
    }

    // Check if updating existing record
    if ($action === 'update') {
        if (!$personal_info->getByUserId($_SESSION['user_id'])) {
            return [
                'success' => false,
                'message' => 'Personal information record not found.',
                'errors' => []
            ];
        }
        
        $old_values = [
            'surname' => $personal_info->surname,
            'first_name' => $personal_info->first_name,
            // Add other fields as needed for audit
        ];
        
        // Update the record
        $personal_info->surname = $_POST['surname'] ?? '';
        $personal_info->first_name = $_POST['first_name'] ?? '';
        // ... (set other fields again for update)
        
        if ($personal_info->update()) {
            // Log the update
            $audit->log(
                $_SESSION['user_id'],
                'UPDATE',
                'personal_information',
                $personal_info->id,
                $old_values,
                $_POST,
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_USER_AGENT']
            );
            
            return [
                'success' => true,
                'message' => 'Personal information updated successfully.',
                'redirect' => 'family-background.php'
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to update personal information.',
                'errors' => []
            ];
        }
    } else {
        // Create new record
        if ($personal_info->userHasPersonalInfo($_SESSION['user_id'])) {
            return [
                'success' => false,
                'message' => 'Personal information already exists. Use update instead.',
                'errors' => []
            ];
        }
        
        if ($personal_info->create()) {
            // Log the creation
            $audit->log(
                $_SESSION['user_id'],
                'CREATE',
                'personal_information',
                $personal_info->id,
                null,
                $_POST,
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_USER_AGENT']
            );
            
            return [
                'success' => true,
                'message' => 'Personal information saved successfully.',
                'redirect' => 'family-background.php'
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to save personal information.',
                'errors' => []
            ];
        }
    }
}

/**
 * Process Family Background Section
 */
function processFamilyBackground($db, $audit) {
    // This will be implemented when we create the family background section
    return [
        'success' => true,
        'message' => 'Family background saved successfully.',
        'redirect' => 'educational-background.php'
    ];
}

/**
 * Process Educational Background Section
 */
function processEducationalBackground($db, $audit) {
    // This will be implemented when we create the educational background section
    return [
        'success' => true,
        'message' => 'Educational background saved successfully.',
        'redirect' => 'view.php'
    ];
}
?>
