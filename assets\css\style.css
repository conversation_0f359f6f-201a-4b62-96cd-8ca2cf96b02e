/* State University of Northern Negros PDS System Styles */

:root {
    --primary-color: #1e3a8a;
    --secondary-color: #3b82f6;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --light-gray: #f8fafc;
    --medium-gray: #e2e8f0;
    --dark-gray: #64748b;
    --text-color: #1e293b;
    --border-radius: 8px;
    --box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-gray);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 1rem 0;
    box-shadow: var(--box-shadow);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #1e40af;
}

.btn-secondary {
    background-color: var(--medium-gray);
    color: var(--text-color);
}

.btn-secondary:hover {
    background-color: #cbd5e1;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #dc2626;
}

/* Form Styles */
.form-container {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin: 2rem 0;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--medium-gray);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

/* Table Styles */
.table-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    margin: 2rem 0;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--medium-gray);
}

.table th {
    background-color: var(--light-gray);
    font-weight: 600;
    color: var(--text-color);
}

.table tbody tr:hover {
    background-color: var(--light-gray);
}

/* Alert Styles */
.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin: 1rem 0;
    border-left: 4px solid;
}

.alert-success {
    background-color: #ecfdf5;
    border-color: var(--success-color);
    color: #065f46;
}

.alert-danger {
    background-color: #fef2f2;
    border-color: var(--danger-color);
    color: #991b1b;
}

.alert-warning {
    background-color: #fffbeb;
    border-color: var(--warning-color);
    color: #92400e;
}

/* Card Styles */
.card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    margin: 1rem 0;
}

.card-header {
    background-color: var(--light-gray);
    padding: 1rem;
    border-bottom: 1px solid var(--medium-gray);
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Navigation Styles */
.nav {
    background: white;
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

.nav-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    border-right: 1px solid var(--medium-gray);
}

.nav-link {
    display: block;
    padding: 1rem 1.5rem;
    text-decoration: none;
    color: var(--text-color);
    transition: background-color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    background-color: var(--light-gray);
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .nav-list {
        flex-direction: column;
    }
    
    .nav-item {
        border-right: none;
        border-bottom: 1px solid var(--medium-gray);
    }
    
    .table-container {
        overflow-x: auto;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
