/* Personal Data Sheet Form Styles */

.pds-container {
    max-width: 1000px;
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    min-height: 100vh;
}

.pds-header {
    background: white;
    padding: 2rem;
    border-bottom: 3px solid var(--primary-color);
    text-align: center;
}

.header-logo {
    margin-bottom: 1rem;
}

.university-logo {
    height: 80px;
    width: auto;
}

.header-content h1 {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 0.5rem 0;
    text-transform: uppercase;
}

.header-content h2 {
    font-size: 1.4rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 0.5rem 0;
    text-transform: uppercase;
    border: 2px solid var(--primary-color);
    padding: 0.5rem;
    display: inline-block;
}

.warning-text {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 4px;
    color: #856404;
    font-size: 0.9rem;
}

.instruction-text {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 4px;
    color: #0c5460;
    font-size: 0.9rem;
}

.pds-section {
    padding: 2rem;
    border-bottom: 1px solid var(--medium-gray);
}

.section-header {
    background: var(--primary-color);
    color: white;
    padding: 1rem;
    margin: -2rem -2rem 2rem -2rem;
    font-weight: bold;
}

.section-header h3 {
    margin: 0;
    font-size: 1.2rem;
    text-transform: uppercase;
}

.form-grid {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    align-items: end;
}

.form-row.full-width {
    grid-template-columns: 1fr;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    text-transform: uppercase;
}

.form-control {
    padding: 0.75rem;
    border: 2px solid var(--medium-gray);
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.form-control:required {
    border-left: 4px solid var(--warning-color);
}

.form-control:required:valid {
    border-left: 4px solid var(--success-color);
}

.form-control.error {
    border-color: var(--danger-color);
    background-color: #fef2f2;
}

.error-message {
    color: var(--danger-color);
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.address-section {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid var(--medium-gray);
}

.address-section h4 {
    margin: 0 0 1rem 0;
    color: var(--primary-color);
    font-size: 1rem;
    text-transform: uppercase;
    font-weight: bold;
}

.form-navigation {
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--light-gray);
    border-top: 1px solid var(--medium-gray);
}

.section-progress {
    background: white;
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--medium-gray);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--medium-gray);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.9rem;
    color: var(--dark-gray);
    text-align: center;
}

/* Table Styles for Dynamic Sections */
.dynamic-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
    background: white;
    border: 2px solid var(--medium-gray);
}

.dynamic-table th,
.dynamic-table td {
    padding: 0.75rem;
    border: 1px solid var(--medium-gray);
    text-align: left;
    vertical-align: top;
}

.dynamic-table th {
    background: var(--primary-color);
    color: white;
    font-weight: bold;
    font-size: 0.9rem;
    text-transform: uppercase;
}

.dynamic-table input,
.dynamic-table select,
.dynamic-table textarea {
    width: 100%;
    border: 1px solid var(--medium-gray);
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.dynamic-table input:focus,
.dynamic-table select:focus,
.dynamic-table textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.add-row-btn {
    background: var(--success-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    margin: 0.5rem 0;
}

.add-row-btn:hover {
    background: #059669;
}

.remove-row {
    background: var(--danger-color);
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
}

.remove-row:hover {
    background: #dc2626;
}

/* File Upload Styles */
.file-upload-area {
    border: 2px dashed var(--medium-gray);
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    background: #fafafa;
    transition: all 0.3s ease;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background: #f0f9ff;
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background: #f0f9ff;
}

.file-upload-icon {
    font-size: 3rem;
    color: var(--medium-gray);
    margin-bottom: 1rem;
}

.file-upload-text {
    color: var(--dark-gray);
    margin-bottom: 1rem;
}

.file-upload-input {
    display: none;
}

.file-upload-btn {
    background: var(--primary-color);
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
}

.file-upload-btn:hover {
    background: #1e40af;
}

.uploaded-files {
    margin-top: 1rem;
}

.uploaded-file {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: white;
    border: 1px solid var(--medium-gray);
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.file-remove {
    background: var(--danger-color);
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .pds-container {
        margin: 0;
        box-shadow: none;
    }
    
    .pds-header {
        padding: 1rem;
    }
    
    .header-content h1 {
        font-size: 1.4rem;
    }
    
    .header-content h2 {
        font-size: 1.2rem;
    }
    
    .pds-section {
        padding: 1rem;
    }
    
    .section-header {
        margin: -1rem -1rem 1rem -1rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-navigation {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .dynamic-table {
        font-size: 0.8rem;
    }
    
    .dynamic-table th,
    .dynamic-table td {
        padding: 0.5rem;
    }
}

/* Print Styles */
@media print {
    .form-navigation,
    .section-progress,
    .add-row-btn,
    .remove-row,
    .file-upload-area {
        display: none !important;
    }
    
    .pds-container {
        box-shadow: none;
        margin: 0;
    }
    
    .pds-section {
        page-break-inside: avoid;
        border-bottom: 1px solid #000;
    }
    
    .form-control {
        border: 1px solid #000;
        background: transparent;
    }
    
    .dynamic-table {
        border: 2px solid #000;
    }
    
    .dynamic-table th,
    .dynamic-table td {
        border: 1px solid #000;
    }
}

/* Validation Styles */
.field-required::after {
    content: " *";
    color: var(--danger-color);
}

.validation-summary {
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
}

.validation-summary h4 {
    color: var(--danger-color);
    margin: 0 0 0.5rem 0;
}

.validation-summary ul {
    margin: 0;
    padding-left: 1.5rem;
    color: var(--danger-color);
}

/* Success States */
.form-control.success {
    border-color: var(--success-color);
    background-color: #f0fdf4;
}

.success-message {
    color: var(--success-color);
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

/* Loading States */
.form-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.form-loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
