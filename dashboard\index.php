<?php
/**
 * Dashboard for State University of Northern Negros PDS System
 */

require_once '../config/config.php';
require_once '../classes/User.php';

// Require login
require_login();

$page_title = 'Dashboard';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get user statistics
    $user = new User($db);
    
    if ($_SESSION['user_role'] === 'admin') {
        // Admin dashboard statistics
        $total_users = $user->getTotalCount();
        
        // Get recent registrations
        $recent_users = $user->getAll(5, 0);
        
        // Get PDS completion statistics
        $pds_stats_query = "SELECT 
            COUNT(DISTINCT pi.user_id) as completed_pds,
            COUNT(DISTINCT u.id) as total_employees
            FROM users u 
            LEFT JOIN personal_information pi ON u.id = pi.user_id 
            WHERE u.role = 'employee'";
        $stmt = $db->prepare($pds_stats_query);
        $stmt->execute();
        $pds_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    } else {
        // Employee dashboard - check PDS completion status
        $pds_check_query = "SELECT COUNT(*) as has_pds FROM personal_information WHERE user_id = :user_id";
        $stmt = $db->prepare($pds_check_query);
        $stmt->bindParam(':user_id', $_SESSION['user_id']);
        $stmt->execute();
        $pds_status = $stmt->fetch(PDO::FETCH_ASSOC);
        $has_pds = $pds_status['has_pds'] > 0;
    }
    
} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $error_message = "Unable to load dashboard data.";
}

include '../includes/header.php';
?>

<div class="dashboard-content">
    <div class="dashboard-header">
        <h1>
            <i class="fas fa-tachometer-alt"></i> Dashboard
        </h1>
        <p>Welcome back, <?php echo htmlspecialchars($_SESSION['user_name']); ?>!</p>
    </div>

    <?php if ($_SESSION['user_role'] === 'admin'): ?>
        <!-- Admin Dashboard -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo $total_users ?? 0; ?></h3>
                    <p>Total Users</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo $pds_stats['completed_pds'] ?? 0; ?></h3>
                    <p>Completed PDS</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-user-tie"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo $pds_stats['total_employees'] ?? 0; ?></h3>
                    <p>Total Employees</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="stat-content">
                    <h3>
                        <?php 
                        $completion_rate = 0;
                        if (isset($pds_stats) && $pds_stats['total_employees'] > 0) {
                            $completion_rate = round(($pds_stats['completed_pds'] / $pds_stats['total_employees']) * 100);
                        }
                        echo $completion_rate; 
                        ?>%
                    </h3>
                    <p>Completion Rate</p>
                </div>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="card-header">
                    <h3><i class="fas fa-user-plus"></i> Recent Registrations</h3>
                </div>
                <div class="card-body">
                    <?php if (!empty($recent_users)): ?>
                        <div class="user-list">
                            <?php foreach ($recent_users as $recent_user): ?>
                                <div class="user-item">
                                    <div class="user-info">
                                        <strong><?php echo htmlspecialchars($recent_user['first_name'] . ' ' . $recent_user['last_name']); ?></strong>
                                        <small><?php echo htmlspecialchars($recent_user['employee_id']); ?></small>
                                    </div>
                                    <div class="user-meta">
                                        <span class="badge badge-<?php echo $recent_user['status'] === 'active' ? 'success' : 'warning'; ?>">
                                            <?php echo ucfirst($recent_user['status']); ?>
                                        </span>
                                        <small><?php echo date('M j, Y', strtotime($recent_user['created_at'])); ?></small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-center">No recent registrations.</p>
                    <?php endif; ?>
                </div>
            </div>

            <div class="dashboard-card">
                <div class="card-header">
                    <h3><i class="fas fa-tasks"></i> Quick Actions</h3>
                </div>
                <div class="card-body">
                    <div class="quick-actions">
                        <a href="../admin/employees.php" class="action-btn">
                            <i class="fas fa-users"></i>
                            <span>Manage Employees</span>
                        </a>
                        <a href="../admin/reports.php" class="action-btn">
                            <i class="fas fa-chart-bar"></i>
                            <span>View Reports</span>
                        </a>
                        <a href="../admin/export.php" class="action-btn">
                            <i class="fas fa-download"></i>
                            <span>Export Data</span>
                        </a>
                        <a href="../admin/settings.php" class="action-btn">
                            <i class="fas fa-cog"></i>
                            <span>System Settings</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

    <?php else: ?>
        <!-- Employee Dashboard -->
        <div class="employee-dashboard">
            <?php if (!$has_pds): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Action Required:</strong> You haven't completed your Personal Data Sheet yet. 
                    Please complete it to ensure your records are up to date.
                </div>
            <?php endif; ?>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-file-alt"></i> Personal Data Sheet</h3>
                    </div>
                    <div class="card-body">
                        <div class="pds-status">
                            <?php if ($has_pds): ?>
                                <div class="status-complete">
                                    <i class="fas fa-check-circle"></i>
                                    <h4>PDS Completed</h4>
                                    <p>Your Personal Data Sheet is complete and up to date.</p>
                                </div>
                            <?php else: ?>
                                <div class="status-incomplete">
                                    <i class="fas fa-exclamation-circle"></i>
                                    <h4>PDS Incomplete</h4>
                                    <p>Please complete your Personal Data Sheet.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="pds-actions">
                            <?php if ($has_pds): ?>
                                <a href="../pds/view.php" class="btn btn-primary">
                                    <i class="fas fa-eye"></i> View PDS
                                </a>
                                <a href="../pds/edit.php" class="btn btn-secondary">
                                    <i class="fas fa-edit"></i> Edit PDS
                                </a>
                                <a href="../pds/export.php" class="btn btn-secondary">
                                    <i class="fas fa-download"></i> Export PDF
                                </a>
                            <?php else: ?>
                                <a href="../pds/create.php" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Create PDS
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-user"></i> Profile Information</h3>
                    </div>
                    <div class="card-body">
                        <div class="profile-info">
                            <div class="info-item">
                                <label>Employee ID:</label>
                                <span><?php echo htmlspecialchars($_SESSION['employee_id']); ?></span>
                            </div>
                            <div class="info-item">
                                <label>Name:</label>
                                <span><?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                            </div>
                            <div class="info-item">
                                <label>Role:</label>
                                <span class="badge badge-primary"><?php echo ucfirst($_SESSION['user_role']); ?></span>
                            </div>
                        </div>
                        
                        <div class="profile-actions">
                            <a href="../profile/settings.php" class="btn btn-secondary">
                                <i class="fas fa-cog"></i> Account Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.dashboard-content {
    padding: 2rem 0;
}

.dashboard-header {
    margin-bottom: 2rem;
}

.dashboard-header h1 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    background: var(--primary-color);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 0;
}

.stat-content p {
    margin: 0;
    color: var(--dark-gray);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.dashboard-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.card-header {
    background: var(--light-gray);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--medium-gray);
}

.card-header h3 {
    margin: 0;
    color: var(--primary-color);
}

.card-body {
    padding: 1.5rem;
}

.user-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.user-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--light-gray);
    border-radius: var(--border-radius);
}

.user-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-success {
    background: var(--success-color);
    color: white;
}

.badge-warning {
    background: var(--warning-color);
    color: white;
}

.badge-primary {
    background: var(--primary-color);
    color: white;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: var(--light-gray);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--text-color);
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: var(--medium-gray);
    transform: translateY(-2px);
}

.action-btn i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.pds-status {
    text-align: center;
    margin-bottom: 1.5rem;
}

.status-complete i {
    color: var(--success-color);
    font-size: 3rem;
    margin-bottom: 0.5rem;
}

.status-incomplete i {
    color: var(--warning-color);
    font-size: 3rem;
    margin-bottom: 0.5rem;
}

.pds-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.profile-info {
    margin-bottom: 1.5rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--medium-gray);
}

.info-item:last-child {
    border-bottom: none;
}

.info-item label {
    font-weight: 500;
    color: var(--dark-gray);
}

.profile-actions {
    text-align: center;
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .pds-actions {
        flex-direction: column;
    }
}
</style>

<?php include '../includes/footer.php'; ?>
