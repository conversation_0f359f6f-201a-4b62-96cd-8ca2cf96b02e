<?php
/**
 * Registration Page for State University of Northern Negros PDS System
 */

require_once '../config/config.php';
require_once '../classes/User.php';

// Redirect if already logged in
if (is_logged_in()) {
    header('Location: ' . BASE_URL . 'dashboard/');
    exit();
}

$error_message = '';
$success_message = '';
$form_data = [];

// Process registration form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        // Sanitize and validate input
        $form_data = [
            'employee_id' => sanitize_input($_POST['employee_id'] ?? ''),
            'username' => sanitize_input($_POST['username'] ?? ''),
            'email' => sanitize_input($_POST['email'] ?? ''),
            'first_name' => sanitize_input($_POST['first_name'] ?? ''),
            'last_name' => sanitize_input($_POST['last_name'] ?? ''),
            'password' => $_POST['password'] ?? '',
            'confirm_password' => $_POST['confirm_password'] ?? ''
        ];

        // Validation
        $errors = [];

        if (empty($form_data['employee_id'])) {
            $errors[] = 'Employee ID is required.';
        } elseif (!preg_match('/^[A-Z0-9]{6,20}$/', $form_data['employee_id'])) {
            $errors[] = 'Employee ID must be 6-20 characters long and contain only uppercase letters and numbers.';
        }

        if (empty($form_data['username'])) {
            $errors[] = 'Username is required.';
        } elseif (strlen($form_data['username']) < 3 || strlen($form_data['username']) > 50) {
            $errors[] = 'Username must be between 3 and 50 characters.';
        } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $form_data['username'])) {
            $errors[] = 'Username can only contain letters, numbers, and underscores.';
        }

        if (empty($form_data['email'])) {
            $errors[] = 'Email is required.';
        } elseif (!filter_var($form_data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Please enter a valid email address.';
        }

        if (empty($form_data['first_name'])) {
            $errors[] = 'First name is required.';
        }

        if (empty($form_data['last_name'])) {
            $errors[] = 'Last name is required.';
        }

        if (empty($form_data['password'])) {
            $errors[] = 'Password is required.';
        } elseif (strlen($form_data['password']) < 8) {
            $errors[] = 'Password must be at least 8 characters long.';
        } elseif (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/', $form_data['password'])) {
            $errors[] = 'Password must contain at least one uppercase letter, one lowercase letter, and one number.';
        }

        if ($form_data['password'] !== $form_data['confirm_password']) {
            $errors[] = 'Passwords do not match.';
        }

        if (empty($errors)) {
            try {
                $database = new Database();
                $db = $database->getConnection();
                $user = new User($db);

                // Check for existing records
                if ($user->employeeIdExists($form_data['employee_id'])) {
                    $errors[] = 'Employee ID already exists.';
                }

                if ($user->usernameExists($form_data['username'])) {
                    $errors[] = 'Username already exists.';
                }

                if ($user->emailExists($form_data['email'])) {
                    $errors[] = 'Email already exists.';
                }

                if (empty($errors)) {
                    // Create new user
                    $user->employee_id = $form_data['employee_id'];
                    $user->username = $form_data['username'];
                    $user->email = $form_data['email'];
                    $user->password_hash = $form_data['password']; // Will be hashed in create method
                    $user->role = 'employee'; // Default role
                    $user->first_name = $form_data['first_name'];
                    $user->last_name = $form_data['last_name'];
                    $user->status = 'active';

                    if ($user->create()) {
                        $success_message = 'Registration successful! You can now log in with your credentials.';
                        $form_data = []; // Clear form data
                    } else {
                        $error_message = 'Registration failed. Please try again.';
                    }
                }
            } catch (Exception $e) {
                $error_message = 'Registration failed. Please try again later.';
                error_log("Registration error: " . $e->getMessage());
            }
        }

        if (!empty($errors)) {
            $error_message = implode('<br>', $errors);
        }
    }
}

$page_title = 'Register';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .register-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            padding: 2rem 0;
        }
        .register-form {
            background: white;
            padding: 3rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 500px;
        }
        .register-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .register-header h1 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        .register-header p {
            color: var(--dark-gray);
            font-size: 0.9rem;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        .password-requirements {
            font-size: 0.8rem;
            color: var(--dark-gray);
            margin-top: 0.5rem;
        }
        .register-footer {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid var(--medium-gray);
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-form">
            <div class="register-header">
                <i class="fas fa-university" style="font-size: 3rem; color: var(--primary-color); margin-bottom: 1rem;"></i>
                <h1>Create Account</h1>
                <p>State University of Northern Negros<br>Personal Data Sheet System</p>
            </div>

            <?php if ($error_message): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
                    <div class="mt-2">
                        <a href="login.php" class="btn btn-primary">Go to Login</a>
                    </div>
                </div>
            <?php else: ?>

            <form method="POST" data-validate="true">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                
                <div class="form-group">
                    <label for="employee_id">Employee ID</label>
                    <input type="text" name="employee_id" id="employee_id" class="form-control" 
                           placeholder="e.g., EMP001" value="<?php echo htmlspecialchars($form_data['employee_id'] ?? ''); ?>" 
                           required pattern="[A-Z0-9]{6,20}" title="6-20 characters, uppercase letters and numbers only">
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="first_name">First Name</label>
                        <input type="text" name="first_name" id="first_name" class="form-control" 
                               value="<?php echo htmlspecialchars($form_data['first_name'] ?? ''); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="last_name">Last Name</label>
                        <input type="text" name="last_name" id="last_name" class="form-control" 
                               value="<?php echo htmlspecialchars($form_data['last_name'] ?? ''); ?>" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" name="username" id="username" class="form-control" 
                           value="<?php echo htmlspecialchars($form_data['username'] ?? ''); ?>" 
                           required pattern="[a-zA-Z0-9_]{3,50}" title="3-50 characters, letters, numbers, and underscores only">
                </div>

                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" name="email" id="email" class="form-control" 
                           value="<?php echo htmlspecialchars($form_data['email'] ?? ''); ?>" required>
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" name="password" id="password" class="form-control" required>
                    <div class="password-requirements">
                        Password must be at least 8 characters long and contain uppercase, lowercase, and numbers.
                    </div>
                </div>

                <div class="form-group">
                    <label for="confirm_password">Confirm Password</label>
                    <input type="password" name="confirm_password" id="confirm_password" class="form-control" required>
                </div>

                <button type="submit" class="btn btn-primary" style="width: 100%; padding: 12px;">
                    <i class="fas fa-user-plus"></i> Create Account
                </button>
            </form>

            <?php endif; ?>

            <div class="register-footer">
                <p>Already have an account? <a href="login.php" style="color: var(--secondary-color);">Sign in here</a></p>
                <small style="color: var(--dark-gray);">
                    &copy; <?php echo date('Y'); ?> State University of Northern Negros
                </small>
            </div>
        </div>
    </div>

    <script src="<?php echo BASE_URL; ?>assets/js/main.js"></script>
</body>
</html>
