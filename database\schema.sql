-- State University of Northern Negros Personal Data Sheet Database Schema
-- Created: 2025-07-12

CREATE DATABASE IF NOT EXISTS sunegros_pds CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE sunegros_pds;

-- Users table for authentication and role management
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id VARCHAR(20) UNIQUE NOT NULL,
    username VARCHA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('admin', 'employee') DEFAULT 'employee',
    first_name VA<PERSON>HAR(50) NOT NULL,
    last_name VA<PERSON>HA<PERSON>(50) NOT NULL,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIM<PERSON><PERSON><PERSON> NULL,
    password_reset_token VARCHAR(255) NULL,
    password_reset_expires TIMESTAMP NULL
);

-- Personal Information (Main PDS table)
CREATE TABLE personal_information (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    surname VARCHAR(50) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    middle_name VARCHAR(50),
    name_extension VARCHAR(10),
    date_of_birth DATE NOT NULL,
    place_of_birth VARCHAR(100) NOT NULL,
    sex ENUM('Male', 'Female') NOT NULL,
    civil_status ENUM('Single', 'Married', 'Widowed', 'Separated', 'Others') NOT NULL,
    height DECIMAL(5,2),
    weight DECIMAL(5,2),
    blood_type VARCHAR(5),
    gsis_id VARCHAR(20),
    pag_ibig_id VARCHAR(20),
    philhealth_no VARCHAR(20),
    sss_no VARCHAR(20),
    tin_no VARCHAR(20),
    agency_employee_no VARCHAR(20),
    citizenship VARCHAR(50) NOT NULL,
    dual_citizenship_country VARCHAR(50),
    residential_address TEXT NOT NULL,
    residential_zipcode VARCHAR(10),
    permanent_address TEXT NOT NULL,
    permanent_zipcode VARCHAR(10),
    telephone_no VARCHAR(20),
    mobile_no VARCHAR(20),
    email_address VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_personal (user_id)
);

-- Family Background - Spouse Information
CREATE TABLE spouse_information (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    surname VARCHAR(50),
    first_name VARCHAR(50),
    middle_name VARCHAR(50),
    name_extension VARCHAR(10),
    occupation VARCHAR(100),
    employer_business_name VARCHAR(100),
    business_address TEXT,
    telephone_no VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_spouse (user_id)
);

-- Family Background - Children Information
CREATE TABLE children_information (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    full_name VARCHAR(150) NOT NULL,
    date_of_birth DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Family Background - Parents Information
CREATE TABLE parents_information (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    parent_type ENUM('father', 'mother') NOT NULL,
    surname VARCHAR(50) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    middle_name VARCHAR(50),
    name_extension VARCHAR(10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Educational Background
CREATE TABLE educational_background (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    level ENUM('Elementary', 'Secondary', 'Vocational/Trade Course', 'College', 'Graduate Studies') NOT NULL,
    school_name VARCHAR(200) NOT NULL,
    basic_education_degree VARCHAR(100),
    period_from YEAR,
    period_to YEAR,
    highest_level_units_earned VARCHAR(50),
    year_graduated YEAR,
    scholarship_academic_honors VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Civil Service Eligibility
CREATE TABLE civil_service_eligibility (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    career_service VARCHAR(200) NOT NULL,
    rating DECIMAL(5,2),
    date_of_examination DATE,
    place_of_examination VARCHAR(200),
    license_number VARCHAR(50),
    date_of_validity DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Work Experience
CREATE TABLE work_experience (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    position_title VARCHAR(200) NOT NULL,
    department_agency_office_company VARCHAR(200) NOT NULL,
    monthly_salary DECIMAL(10,2),
    salary_job_pay_grade VARCHAR(20),
    status_of_appointment VARCHAR(50),
    govt_service ENUM('Y', 'N') DEFAULT 'N',
    period_from DATE,
    period_to DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Voluntary Work or Involvement in Civic / Non-Government / People / Voluntary Organization/s
CREATE TABLE voluntary_work (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    organization_name VARCHAR(200) NOT NULL,
    organization_address TEXT,
    period_from DATE,
    period_to DATE,
    number_of_hours DECIMAL(6,2),
    position_nature_of_work VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Learning and Development (L&D) Interventions/Training Programs Attended
CREATE TABLE training_programs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    period_from DATE,
    period_to DATE,
    number_of_hours DECIMAL(6,2),
    type_of_ld ENUM('Managerial', 'Supervisory', 'Technical', 'Foundational') NOT NULL,
    conducted_sponsored_by VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Other Information
CREATE TABLE other_skills (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    skill_type ENUM('Special Skills and Hobbies', 'Non-Academic Distinctions / Recognition', 'Membership in Association/Organization') NOT NULL,
    description TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- References
CREATE TABLE references (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    full_name VARCHAR(150) NOT NULL,
    address TEXT NOT NULL,
    telephone_no VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Government Issued ID
CREATE TABLE government_id (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    government_issued_id VARCHAR(100),
    id_license_passport_no VARCHAR(50),
    date_place_of_issuance VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_gov_id (user_id)
);

-- File Uploads
CREATE TABLE file_uploads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    file_type ENUM('profile_photo', 'signature', 'supporting_document') NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Audit Trail
CREATE TABLE audit_trail (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    action ENUM('CREATE', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT') NOT NULL,
    table_name VARCHAR(50),
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_users_employee_id ON users(employee_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_personal_info_user_id ON personal_information(user_id);
CREATE INDEX idx_educational_bg_user_id ON educational_background(user_id);
CREATE INDEX idx_work_exp_user_id ON work_experience(user_id);
CREATE INDEX idx_audit_trail_user_id ON audit_trail(user_id);
CREATE INDEX idx_audit_trail_created_at ON audit_trail(created_at);

-- Insert default admin user (password: admin123)
INSERT INTO users (employee_id, username, email, password_hash, role, first_name, last_name, status) 
VALUES ('ADMIN001', 'admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'System', 'Administrator', 'active');
